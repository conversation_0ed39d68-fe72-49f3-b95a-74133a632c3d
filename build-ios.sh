#!/bin/bash

# JunkChk iOS Development Build Script
# This script builds the iOS development build with AdMob support

echo "🚀 Building JunkChk iOS Development Build with AdMob..."
echo ""

# Check if we're in the right directory
if [ ! -f "app.json" ]; then
    echo "❌ Error: app.json not found. Please run this script from the project root."
    exit 1
fi

# Check if expo CLI is installed
if ! command -v npx &> /dev/null; then
    echo "❌ Error: npx not found. Please install Node.js and npm."
    exit 1
fi

echo "📱 Starting iOS development build..."
echo "This will:"
echo "  - Build the native iOS app with AdMob support"
echo "  - Install it on the iOS Simulator"
echo "  - Start the development server"
echo ""

# Clear any previous builds
echo "🧹 Clearing previous builds..."
npx expo run:ios --clear

# Check if build was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ iOS Development Build Complete!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. The app 'JunkChk (development)' should now be installed on your iOS Simulator"
    echo "2. Start the development server: npx expo start --dev-client"
    echo "3. Open the development build app (not Expo Go)"
    echo "4. Navigate to Home screen to see the AdMob banner ad"
    echo ""
    echo "🎯 Your AdMob Configuration:"
    echo "  iOS App ID: ca-app-pub-8809398979690427~2928321097"
    echo "  iOS Banner Ad Unit: ca-app-pub-8809398979690427/3899304178"
    echo ""
else
    echo ""
    echo "❌ Build failed. Common solutions:"
    echo "1. Make sure Xcode is installed and updated"
    echo "2. Make sure iOS Simulator is available"
    echo "3. Try running: npx expo doctor"
    echo "4. Check the error messages above"
    echo ""
fi
