import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Typography from './Typography';
import { Colors, Spacing, Shadow } from '../theme';
import { Grade, Ingredient, Effects, InfoCard } from '../context/types';

/**
 * InfoCards Component
 *
 * Displays 4 dynamic information cards in a 2x2 grid layout that replace the rating justification section.
 * Cards show the most useful product information as determined by AI analysis:
 *
 * - Cards are dynamically generated by the AI based on product type and context
 * - Content includes relevant metrics like calories, protein, SPF, allergen alerts, etc.
 * - Colors and icons are chosen by AI to indicate safety levels and importance
 * - Prioritizes critical information like allergy warnings and safety concerns
 *
 * The AI determines what information would be most valuable for each specific product.
 */

interface InfoCardsProps {
  apiResponse: any;
  grade: Grade;
  goodIngredients: Ingredient[];
  badIngredients: Ingredient[];
  effects?: Effects;
  productName: string;
  savedInfoCards?: InfoCard[]; // For history items
}

// Product category detection
type ProductCategory = 'food' | 'cosmetic' | 'unknown';

const detectProductCategory = (apiResponse: any, productName: string): ProductCategory => {
  // First check if AI explicitly provided category
  if (apiResponse?.product_category) {
    const category = apiResponse.product_category.toLowerCase();
    if (category.includes('cosmetic') || category.includes('beauty') || category.includes('skincare')) {
      return 'cosmetic';
    }
    if (category.includes('food') || category.includes('edible') || category.includes('nutrition')) {
      return 'food';
    }
  }

  // Check product name and analysis for cosmetic keywords
  const allText = `${productName} ${JSON.stringify(apiResponse || {})}`.toLowerCase();
  const cosmeticKeywords = [
    'cream', 'lotion', 'serum', 'moisturizer', 'cleanser', 'toner', 'sunscreen', 'spf',
    'foundation', 'concealer', 'lipstick', 'mascara', 'eyeshadow', 'blush', 'powder',
    'shampoo', 'conditioner', 'soap', 'body wash', 'face wash', 'scrub', 'mask',
    'anti-aging', 'skincare', 'beauty', 'cosmetic', 'makeup', 'skin care'
  ];

  const foodKeywords = [
    'calories', 'protein', 'carbs', 'fat', 'sugar', 'sodium', 'fiber', 'vitamin',
    'nutrition', 'food', 'snack', 'drink', 'beverage', 'meal', 'edible'
  ];

  const cosmeticMatches = cosmeticKeywords.filter(keyword => allText.includes(keyword)).length;
  const foodMatches = foodKeywords.filter(keyword => allText.includes(keyword)).length;

  if (cosmeticMatches > foodMatches && cosmeticMatches > 0) {
    return 'cosmetic';
  }
  if (foodMatches > 0) {
    return 'food';
  }

  return 'unknown';
};

// Use the InfoCard interface from types
type CardData = InfoCard;

// Helper function to get background color from main color
const getBackgroundColor = (color: string): string => {
  // Handle undefined or null colors
  if (!color) {
    return 'rgba(0, 0, 0, 0.05)';
  }

  // Convert hex color to rgba with low opacity
  if (color.startsWith('#') && color.length >= 7) {
    const hex = color.slice(1);
    const r = parseInt(hex.substring(0, 2), 16) || 0;
    const g = parseInt(hex.substring(2, 4), 16) || 0;
    const b = parseInt(hex.substring(4, 6), 16) || 0;
    return `rgba(${r}, ${g}, ${b}, 0.1)`;
  }

  // Handle common color names
  const colorMap: { [key: string]: string } = {
    red: 'rgba(255, 0, 0, 0.1)',
    green: 'rgba(0, 204, 0, 0.1)',
    blue: 'rgba(0, 122, 255, 0.1)',
    orange: 'rgba(255, 165, 0, 0.1)',
    yellow: 'rgba(255, 215, 0, 0.1)',
  };

  const lowerColor = color.toLowerCase();
  if (colorMap[lowerColor]) {
    return colorMap[lowerColor];
  }

  // Fallback
  return 'rgba(0, 0, 0, 0.05)';
};

const InfoCards: React.FC<InfoCardsProps> = ({
  apiResponse,
  grade,
  goodIngredients = [],
  badIngredients = [],
  effects,
  productName = 'Unknown Product',
  savedInfoCards,
}) => {
  // Detect product category
  const productCategory = detectProductCategory(apiResponse, productName);

  // Extract calories and protein from AI-generated cards
  const extractNutritionFromAICards = () => {
    if (!apiResponse?.info_cards || !Array.isArray(apiResponse.info_cards)) {
      return { calories: 'See Package', protein: 'Check Label' };
    }

    const caloriesCard = apiResponse.info_cards.find((card: any) =>
      card.title?.toLowerCase() === 'calories'
    );
    const proteinCard = apiResponse.info_cards.find((card: any) =>
      card.title?.toLowerCase() === 'protein'
    );

    // Extract values and clean them up
    let caloriesValue = caloriesCard?.value || 'See Package';
    let proteinValue = proteinCard?.value || 'Check Label';

    // Clean up values - remove "kcal" suffix if present since we'll show it in title
    if (typeof caloriesValue === 'string' && caloriesValue.includes('kcal')) {
      caloriesValue = caloriesValue.replace(/\s*kcal/i, '');
    }

    return {
      calories: caloriesValue,
      protein: proteinValue
    };
  };

  // Create category-appropriate mandatory cards
  const getMandatoryCards = (): CardData[] => {
    // If we have saved cards from history, use the first 2
    if (savedInfoCards && savedInfoCards.length >= 2) {
      return savedInfoCards.slice(0, 2);
    }

    // Category-specific mandatory cards
    if (productCategory === 'cosmetic') {
      // For cosmetics, extract skin type and safety from AI cards if available
      const skinTypeCard = apiResponse?.info_cards?.find((card: any) =>
        card.title?.toLowerCase().includes('skin')
      );
      const safetyCard = apiResponse?.info_cards?.find((card: any) =>
        card.title?.toLowerCase().includes('safety')
      );

      return [
        {
          title: 'Skin Type',
          value: skinTypeCard?.value || 'All Types',
          icon: 'person-outline',
          color: skinTypeCard?.color || '#FF6B35',
          priority: 'high'
        },
        {
          title: 'Safety',
          value: safetyCard?.value || grade || 'C',
          icon: 'shield-checkmark',
          color: safetyCard?.color || (grade === 'A' ? Colors.GradeA : grade === 'B' ? Colors.GradeB : Colors.GradeC),
          priority: 'high'
        }
      ];
    }

    // For food products, extract nutrition values from AI cards
    const nutrition = extractNutritionFromAICards();
    return [
      {
        title: 'Calories',
        value: nutrition.calories,
        icon: 'flame',
        color: '#FF6B35',
        priority: 'high'
      },
      {
        title: 'Protein',
        value: nutrition.protein,
        icon: 'fitness',
        color: '#00CC00',
        priority: 'high'
      }
    ];
  };

  // Get smart cards from saved data, AI, or create category-appropriate fallback
  const getSmartCards = (): CardData[] => {
    // First priority: Use saved cards from history (skip first 2 which are mandatory)
    if (savedInfoCards && savedInfoCards.length >= 4) {
      return savedInfoCards.slice(2, 4); // Take cards 3 and 4 from saved data
    }

    // Second priority: Try to get AI-generated cards from API response
    // Filter out any allergen-related cards since we have dedicated allergy indicator
    if (apiResponse?.info_cards && Array.isArray(apiResponse.info_cards) && apiResponse.info_cards.length >= 2) {
      const filteredCards = apiResponse.info_cards.filter((card: any) => {
        const title = (card.title || '').toLowerCase();
        const value = (card.value || '').toLowerCase();

        // Remove allergen-related cards - comprehensive filtering
        const allergenKeywords = [
          'allerg', 'gluten', 'dairy', 'vegan', 'vegetarian', 'peanut', 'nut', 'soy',
          'shellfish', 'fish', 'egg', 'milk', 'wheat', 'sesame', 'sulfite'
        ];

        const isAllergenCard = allergenKeywords.some(keyword =>
          title.includes(keyword) || value.includes(keyword)
        );

        // Also skip the mandatory cards (Calories, Protein for food; Skin Type, Safety for cosmetics)
        const isMandatoryCard = title === 'calories' || title === 'protein' ||
                               title.includes('skin') || title.includes('safety');

        return !isAllergenCard && !isMandatoryCard;
      });

      if (filteredCards.length >= 2) {
        return filteredCards.slice(0, 2);
      }
    }

    // Category-specific fallback cards
    const safeGrade = grade || 'C';
    const getGradeColor = (grade: Grade): string => {
      switch (grade) {
        case 'A': return Colors.GradeA;
        case 'B': return Colors.GradeB;
        case 'C': return Colors.GradeC;
        case 'D': return Colors.GradeD;
        case 'E': return Colors.GradeE;
        default: return Colors.GradeC;
      }
    };

    if (productCategory === 'cosmetic') {
      return [
        {
          title: 'Key Benefits',
          value: apiResponse?.key_benefits || 'Skincare',
          icon: 'sparkles',
          color: '#00CC00',
          priority: 'medium'
        },
        {
          title: 'Concerns',
          value: badIngredients.length.toString(),
          icon: 'alert-circle',
          color: badIngredients.length > 0 ? Colors.Error : Colors.Success,
          priority: 'medium'
        }
      ];
    }

    // Food product fallback cards
    return [
      {
        title: 'Overall Grade',
        value: safeGrade,
        icon: 'star',
        color: getGradeColor(safeGrade),
        priority: 'medium'
      },
      {
        title: 'Concerns',
        value: badIngredients.length.toString(),
        icon: 'alert-circle',
        color: badIngredients.length > 0 ? Colors.Error : Colors.Success,
        priority: 'medium'
      }
    ];
  };

  // Combine mandatory + smart cards
  const getCardData = (): CardData[] => {
    const mandatoryCards = getMandatoryCards();
    const smartCards = getSmartCards();
    return [...mandatoryCards, ...smartCards];
  };

  const cardData = getCardData();

  const renderIcon = (iconName: string, color: string) => {
    // Fallback icon if iconName is invalid
    const safeIconName = iconName || 'information-circle';
    const safeColor = color || Colors.DarkText;

    try {
      return <Ionicons name={safeIconName as any} size={20} color={safeColor} />;
    } catch (error) {
      // Fallback to a safe icon if the provided icon name is invalid
      return <Ionicons name="information-circle" size={20} color={safeColor} />;
    }
  };

  const renderCard = (card: CardData, index: number) => {
    // Ensure we have valid color values
    const cardColor = card.color || Colors.DarkText;
    const iconBackgroundColor = getBackgroundColor(cardColor);

    // Determine if this is a high priority/warning card
    const isHighPriority = card.priority === 'high' || cardColor === '#FF0000' || cardColor === Colors.Error;
    const isWarning = cardColor === '#FF0000' || cardColor === Colors.Error;

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.card,
          Shadow.Small,
          isHighPriority && {
            borderColor: cardColor,
            borderWidth: 1.5,
            backgroundColor: isWarning ? 'rgba(255, 0, 0, 0.02)' : Colors.BackgroundPrimary
          }
        ]}
        activeOpacity={0.95}
        onPress={() => {
          // Could add haptic feedback or detailed view in the future
        }}
      >
        <View style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <View style={[styles.iconContainer, { backgroundColor: iconBackgroundColor }]}>
              {renderIcon(card.icon, cardColor)}
            </View>
          </View>
          <View style={styles.cardInfo}>
            <Typography variant="caption" style={styles.cardTitle}>
              {card.title}
            </Typography>
            <Typography variant="heading2" style={{...styles.cardValue, color: cardColor}}>
              {card.value}
            </Typography>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Get category-appropriate header
  const getSectionHeader = () => {
    if (productCategory === 'cosmetic') {
      return {
        icon: 'sparkles-outline' as const,
        title: 'Beauty & Safety Info'
      };
    }
    return {
      icon: 'analytics-outline' as const,
      title: 'Nutrition & Key Info'
    };
  };

  const sectionHeader = getSectionHeader();

  return (
    <View style={styles.container}>
      {/* Section Header */}
      <View style={styles.sectionHeader}>
        <View style={[styles.headerIconContainer, { backgroundColor: Colors.SurfaceSecondary }]}>
          <Ionicons name={sectionHeader.icon} size={18} color={Colors.DarkText} />
        </View>
        <Typography variant="heading2" style={styles.sectionTitle}>
          {sectionHeader.title}
        </Typography>
      </View>

      {/* Cards Grid */}
      <View style={styles.cardsGrid}>
        {cardData.map((card, index) => renderCard(card, index))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.Large,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  headerIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Small,
    backgroundColor: Colors.SurfaceSecondary,
  },
  sectionTitle: {
    marginLeft: Spacing.Small,
    fontWeight: '600',
    color: Colors.DarkText,
  },
  cardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    width: '48%',
    backgroundColor: Colors.BackgroundPrimary,
    borderRadius: 20,
    marginBottom: Spacing.Medium,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.06)',
    overflow: 'hidden',
  },
  cardContent: {
    padding: Spacing.Medium,
    alignItems: 'flex-start',
    minHeight: 110,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Small,
    justifyContent: 'flex-start',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardTitle: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.LightText,
    textTransform: 'uppercase',
    letterSpacing: 0.8,
    marginBottom: 4,
  },
  cardInfo: {
    flex: 1,
  },
  cardValue: {
    fontSize: 22,
    fontWeight: '700',
    lineHeight: 26,
    letterSpacing: -0.3,
    flexShrink: 1,
  },
});

export default InfoCards;
