import React, { useEffect, memo, useMemo } from 'react';
import { View, StyleSheet, ViewStyle, Platform } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  Easing,
  interpolateColor,
} from 'react-native-reanimated';
import { Colors } from '../theme';
import SafeAnimatedComponent, { getSafeAnimationConfig } from './SafeAnimatedComponent';

interface GlowingElementProps {
  color?: string;
  size?: number;
  intensity?: number;
  speed?: 'slow' | 'medium' | 'fast';
  active?: boolean;
  style?: ViewStyle;
  children?: React.ReactNode;
}

const GlowingElement: React.FC<GlowingElementProps> = memo(({
  color = Colors.DarkText,
  size = 100,
  intensity = 0.5,
  speed = 'medium',
  active = true,
  style,
  children,
}) => {
  // Animation value
  const glowAnimation = useSharedValue(0);

  // Calculate duration based on speed - memoized
  const duration = useMemo(() => {
    switch (speed) {
      case 'slow': return 3000;
      case 'fast': return 1000;
      case 'medium':
      default: return 2000;
    }
  }, [speed]);

  useEffect(() => {
    try {
      if (active) {
        // Use safe animation config
        const safeConfig = getSafeAnimationConfig(duration);

        // Start the glow animation with error handling
        glowAnimation.value = withRepeat(
          withSequence(
            withTiming(1, { duration: safeConfig.duration, easing: Easing.inOut(Easing.ease) }),
            withTiming(0, { duration: safeConfig.duration, easing: Easing.inOut(Easing.ease) })
          ),
          -1, // Infinite repeat
          true // Reverse
        );
      } else {
        // Reset animation when not active
        glowAnimation.value = withTiming(0, { duration: 300 });
      }
    } catch (error) {
      console.warn('GlowingElement: Animation setup error:', error);
      // Fallback to no animation
      glowAnimation.value = active ? 0.5 : 0;
    }

    return () => {
      try {
        // Safe cleanup
        glowAnimation.value = 0;
      } catch (error) {
        console.warn('GlowingElement: Cleanup error:', error);
      }
    };
  }, [active, duration]);

  // Create the enhanced glow effect style with safe error handling
  const glowStyle = useAnimatedStyle(() => {
    'worklet';
    // Ensure animation value is properly initialized
    const animationValue = glowAnimation.value ?? 0;

    // Dynamic values that drive the glow (premium version without scale)
    const baseOpacity = 0.4 + animationValue * intensity; // subtle opacity variation
    const shadowRadius = (size / 2) * (1 + animationValue * 0.6); // refined radius for elegant halo
    const shadowSpread = animationValue * 2; // subtle shadow spread

    // Interpolate shadow color for a refined pulse (from lighter to original)
    // Ensure color is valid before interpolation
    const safeColor = color || '#ffffff';
    const dynamicColor = interpolateColor(animationValue, [0, 1], ['#ffffff', safeColor]);

    const common = {
      shadowColor: dynamicColor,
      shadowOpacity: Math.max(0, Math.min(1, baseOpacity * 1.1)), // Clamp opacity between 0-1
      shadowRadius: Math.max(0, shadowRadius), // Ensure positive radius
      shadowOffset: { width: 0, height: shadowSpread }, // subtle vertical shadow
      // Removed scale transform for premium feel
    } as ViewStyle;

    if (Platform.OS === 'android') {
      return {
        ...common,
        elevation: Math.max(0, Math.min(24, baseOpacity * 24)), // Clamp elevation between 0-24
      };
    }

    return common;
  }, [intensity, size, color]);

  // Memoize container style
  const containerStyle = useMemo(() => [styles.container, glowStyle, style], [glowStyle, style]);

  return (
    <SafeAnimatedComponent fallback={<View style={[styles.container, style]}>{children}</View>}>
      <Animated.View style={containerStyle}>
        {children}
      </Animated.View>
    </SafeAnimatedComponent>
  );
});

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default GlowingElement;
