import React, { useState, memo, useCallback, useMemo } from 'react';
import { StyleSheet, ViewStyle, TouchableOpacity, Text, View } from 'react-native';
import { Colors } from '../theme';
import GlowingElement from './GlowingElement';
import { Typography, Spacing, Shadow } from "../theme";

interface GlowingButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'tertiary';
  disabled?: boolean;
  style?: ViewStyle;
  icon?: React.ReactNode;
  glowColor?: string;
  glowIntensity?: number;
  glowOnPress?: boolean;
  alwaysGlow?: boolean;
}

const GlowingButton: React.FC<GlowingButtonProps> = memo(({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
  style,
  icon,
  glowColor,
  glowIntensity = 0.7,
  glowOnPress = false,
  alwaysGlow = false,
}) => {
  const [isPressed, setIsPressed] = useState(false);

  // Determine glow color based on variant if not explicitly provided - memoized
  const glowColorMemo = useMemo(() => {
    if (glowColor) return glowColor;

    switch (variant) {
      case 'primary': return Colors.DarkText;
      case 'secondary': return Colors.LightText;
      case 'tertiary': return Colors.AccentBlue;
      default: return Colors.DarkText;
    }
  }, [glowColor, variant]);

  // Determine if glow should be active - memoized
  const isGlowActive = useMemo(() =>
    alwaysGlow || (glowOnPress && isPressed),
    [alwaysGlow, glowOnPress, isPressed]
  );

  // Handle button press - memoized
  const handlePress = useCallback(() => {
    if (!disabled && onPress) {
      console.log('GlowingButton: handlePress called');
      onPress();
    }
  }, [disabled, onPress]);

  // Memoize press handlers
  const handlePressIn = useCallback(() => setIsPressed(true), []);
  const handlePressOut = useCallback(() => setIsPressed(false), []);

  // Memoize button styles
  const buttonStyle = useMemo(() => [
    styles.button,
    variant === 'primary' && styles.primaryButton,
    variant === 'secondary' && styles.secondaryButton,
    variant === 'tertiary' && styles.tertiaryButton,
    disabled && styles.disabledButton,
    (variant === 'primary' || variant === 'secondary') && Shadow.Small,
    style,
  ], [variant, disabled, style]);

  // Memoize text styles
  const textStyle = useMemo(() => [
    styles.buttonText,
    variant === 'primary' && styles.primaryButtonText,
    variant === 'secondary' && styles.secondaryButtonText,
    variant === 'tertiary' && styles.tertiaryButtonText,
    disabled && styles.disabledButtonText,
  ], [variant, disabled]);

  return (
    <GlowingElement
      color={glowColorMemo}
      intensity={glowIntensity}
      active={isGlowActive}
      style={styles.glowContainer}
    >
      <TouchableOpacity
        style={buttonStyle}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.85} // Premium opacity change
      >
        <View style={styles.buttonContent}>
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          <Text style={textStyle}>
            {title}
          </Text>
        </View>
      </TouchableOpacity>
    </GlowingElement>
  );
});

const styles = StyleSheet.create({
  glowContainer: {
    // No additional styling needed
  },
  button: {
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
    borderRadius: 12, // Medium border radius
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginRight: Spacing.Small,
  },
  primaryButton: {
    backgroundColor: Colors.DarkText,
  },
  secondaryButton: {
    backgroundColor: Colors.SurfaceSecondary,
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
  },
  disabledButton: {
    backgroundColor: Colors.LightText,
    opacity: 0.7,
  },
  buttonText: {
    ...Typography.ButtonText,
    textAlign: 'center',
  },
  primaryButtonText: {
    color: Colors.BackgroundPrimary,
  },
  secondaryButtonText: {
    color: Colors.DarkText,
  },
  tertiaryButtonText: {
    color: Colors.AccentBlue,
  },
  disabledButtonText: {
    color: Colors.SurfaceSecondary,
  },
});

export default GlowingButton;
