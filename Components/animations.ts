import { useEffect } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withRepeat,
  withSequence,
  withDelay,
  Easing,
  interpolate,
  interpolateColor,
} from 'react-native-reanimated';
import { ViewStyle, Platform } from 'react-native';
import {
  IOSEasing,
  PlatformDuration,
  PlatformSpring,
  PlatformShadow,
  PlatformGlow,
  PlatformScale,
  PlatformOpacity
} from './PlatformAnimations';

// Animation timing constants (now platform-specific)
export const AnimationDuration = PlatformDuration;

// Animation easing presets (iOS-like on all platforms)
export const AnimationEasing = IOSEasing;

// Premium button press animation hook (opacity and shadow based)
export const useButtonAnimation = (intensity?: number) => {
  const pressed = useSharedValue(0);
  const actualIntensity = intensity || 1;

  const animatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(pressed.value, [0, 1], [1, 0.85]);
    const shadowOpacity = interpolate(pressed.value, [0, 1], [0.15, 0.05]);
    const shadowRadius = interpolate(pressed.value, [0, 1], [4, 2]);
    const elevation = interpolate(pressed.value, [0, 1], [3, 1]);

    return {
      opacity: opacity * actualIntensity + (1 - actualIntensity),
      shadowOpacity: shadowOpacity * actualIntensity,
      shadowRadius: shadowRadius,
      elevation: Platform.OS === 'android' ? elevation : undefined,
    };
  });

  const onPressIn = () => {
    pressed.value = withSpring(1, PlatformSpring.Default);
  };

  const onPressOut = () => {
    pressed.value = withSpring(0, PlatformSpring.Gentle);
  };

  return { animatedStyle, onPressIn, onPressOut };
};

// Enhanced glow animation hook with iOS-like shadows on Android
export const useGlowAnimation = (intensity = 0.5, duration = AnimationDuration.Long, color = '#000') => {
  const glow = useSharedValue(0);

  useEffect(() => {
    glow.value = withRepeat(
      withSequence(
        withTiming(1, { duration, easing: AnimationEasing.Smooth }),
        withTiming(0, { duration, easing: AnimationEasing.Smooth })
      ),
      -1, // Infinite repeat
      true // Reverse
    );

    return () => {
      // Cleanup
      glow.value = 0;
    };
  }, []);

  const glowStyle = useAnimatedStyle(() => {
    const baseGlow = intensity > 0.6 ? PlatformGlow.Strong :
                     intensity > 0.3 ? PlatformGlow.Medium :
                     PlatformGlow.Subtle;

    const shadowOpacityValue = interpolate(
      glow.value,
      [0, 1],
      [baseGlow.shadowOpacity * 0.3, baseGlow.shadowOpacity]
    );
    const shadowRadiusValue = interpolate(
      glow.value,
      [0, 1],
      [baseGlow.shadowRadius * 0.5, baseGlow.shadowRadius]
    );

    // Enhanced shadow implementation for both platforms
    const shadowStyle = {
      shadowColor: color,
      shadowOpacity: shadowOpacityValue,
      shadowRadius: shadowRadiusValue,
    };

    // Add elevation for Android
    if (Platform.OS === 'android') {
      const elevationValue = interpolate(
        glow.value,
        [0, 1],
        [baseGlow.elevation * 0.3, baseGlow.elevation]
      );
      return {
        ...shadowStyle,
        elevation: elevationValue,
      };
    }

    return shadowStyle;
  });

  return { glowStyle };
};

// Enhanced fade in animation hook with platform-specific timing
export const useFadeInAnimation = (
  delay = 0,
  duration = AnimationDuration.Medium,
  direction: 'up' | 'down' | 'left' | 'right' | 'none' = 'none',
  distance = 20
) => {
  const opacity = useSharedValue(0);
  const translation = useSharedValue(direction !== 'none' ? distance : 0);

  useEffect(() => {
    // Use spring animation for more iOS-like feel
    opacity.value = withDelay(
      delay,
      withSpring(1, PlatformSpring.Gentle)
    );

    if (direction !== 'none') {
      translation.value = withDelay(
        delay,
        withSpring(0, PlatformSpring.Default)
      );
    }

    return () => {
      // Cleanup
      opacity.value = 0;
      if (direction !== 'none') {
        translation.value = distance;
      }
    };
  }, []);

  const fadeStyle = useAnimatedStyle(() => {
    const translateStyle = (): ViewStyle => {
      switch (direction) {
        case 'up':
          return { transform: [{ translateY: translation.value }] };
        case 'down':
          return { transform: [{ translateY: -translation.value }] };
        case 'left':
          return { transform: [{ translateX: translation.value }] };
        case 'right':
          return { transform: [{ translateX: -translation.value }] };
        default:
          return {};
      }
    };

    return {
      opacity: opacity.value,
      ...translateStyle(),
    };
  });

  return { fadeStyle };
};

// Floating animation hook
export const useFloatingAnimation = (
  amplitude = 5,
  duration = AnimationDuration.ExtraLong
) => {
  const position = useSharedValue(0);

  useEffect(() => {
    position.value = withRepeat(
      withTiming(1, { duration, easing: AnimationEasing.Smooth }),
      -1, // Infinite repeat
      true // Reverse
    );

    return () => {
      // Cleanup
      position.value = 0;
    };
  }, []);

  const floatingStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateY: interpolate(position.value, [0, 1], [0, amplitude]) },
      ],
    };
  });

  return { floatingStyle };
};

// Shake animation hook
export const useShakeAnimation = (intensity = 5) => {
  const shake = useSharedValue(0);

  const triggerShake = () => {
    shake.value = withSequence(
      withTiming(-intensity, { duration: 50 }),
      withRepeat(
        withSequence(
          withTiming(intensity, { duration: 100 }),
          withTiming(-intensity, { duration: 100 })
        ),
        2
      ),
      withTiming(0, { duration: 50 })
    );
  };

  const shakeStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: shake.value }],
    };
  });

  return { shakeStyle, triggerShake };
};
