import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing } from '../theme';

interface BackButtonProps {
  onPress: () => void;
  style?: ViewStyle;
  color?: string;
  size?: number;
}

const BackButton: React.FC<BackButtonProps> = ({
  onPress,
  style,
  color = Colors.DarkText,
  size = 24,
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.85} // Premium opacity change
    >
      <Ionicons name="chevron-back" size={size} color={color} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Spacing.Small,
  },
});

export default BackButton;
