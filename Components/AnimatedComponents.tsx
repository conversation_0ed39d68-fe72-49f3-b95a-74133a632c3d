import React, { ReactNode } from 'react';
import { ViewStyle, TextStyle, TouchableOpacity, View, Text, Platform } from 'react-native';
import Animated from 'react-native-reanimated';
import { Colors, Shadow, Spacing, Typography } from '../theme';
import {
  useButtonAnimation,
  useGlowAnimation,
  useFadeInAnimation,
  useFloatingAnimation,
  AnimationDuration
} from './animations';

// Animated Button Component
interface AnimatedButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'tertiary';
  style?: ViewStyle;
  textStyle?: TextStyle;
  disabled?: boolean;
  icon?: ReactNode;
  glowOnPress?: boolean;
  glowIntensity?: number;
  glowColor?: string;
  premiumAnimation?: boolean;
  animationIntensity?: number;
  delayMs?: number;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  style,
  textStyle,
  disabled = false,
  icon,
  glowOnPress = true,
  glowIntensity = 0.3,
  glowColor,
  premiumAnimation = true,
  animationIntensity = 1,
  delayMs = 0,
}) => {
  // Premium button press animation
  const { animatedStyle, onPressIn, onPressOut } = useButtonAnimation(animationIntensity);

  // Optional glow effect
  const { glowStyle } = useGlowAnimation(glowIntensity);

  // Optional fade-in animation
  const { fadeStyle } = useFadeInAnimation(delayMs);

  // Determine button colors based on variant
  const getButtonColor = () => {
    switch (variant) {
      case 'primary':
        return Colors.DarkText;
      case 'secondary':
        return Colors.SurfaceSecondary;
      case 'tertiary':
        return 'transparent';
      default:
        return Colors.DarkText;
    }
  };

  // Determine text color based on variant
  const getTextColor = () => {
    switch (variant) {
      case 'primary':
        return Colors.BackgroundPrimary;
      case 'secondary':
        return Colors.DarkText;
      case 'tertiary':
        return Colors.AccentBlue;
      default:
        return Colors.BackgroundPrimary;
    }
  };

  const buttonColor = getButtonColor();
  const textColor = getTextColor();

  // Create safe shadow style for animated view
  const safeShadowStyle = {
    borderRadius: 12, // Medium border radius
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  };

  return (
    <Animated.View
      style={[
        fadeStyle,
        glowOnPress && glowStyle,
        safeShadowStyle,
        style,
      ]}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={premiumAnimation ? onPressIn : undefined}
        onPressOut={premiumAnimation ? onPressOut : undefined}
        disabled={disabled}
        activeOpacity={1} // Disable default opacity since we handle it with animations
      >
        <Animated.View
          style={[
            {
              backgroundColor: buttonColor,
              paddingVertical: Spacing.Medium,
              paddingHorizontal: Spacing.Large,
              borderRadius: 12, // Medium border radius
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.15,
              shadowRadius: 4,
              elevation: 3,
            },
            premiumAnimation && animatedStyle,
            disabled && { opacity: 0.7, backgroundColor: Colors.LightText },
          ]}
        >
          {icon && <View style={{ marginRight: Spacing.Small }}>{icon}</View>}
          <Text
            style={[
              {
                ...Typography.ButtonText,
                color: textColor,
                textAlign: 'center',
              },
              disabled && { color: Colors.SurfaceSecondary },
              textStyle,
            ]}
          >
            {title}
          </Text>
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Animated Text Component
interface AnimatedTextProps {
  children: React.ReactNode;
  variant?: 'heading1' | 'heading2' | 'bodyText' | 'description' | 'caption' | 'buttonText';
  style?: TextStyle;
  color?: string;
  delayMs?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
}

export const AnimatedText: React.FC<AnimatedTextProps> = ({
  children,
  variant = 'bodyText',
  style,
  color,
  delayMs = 0,
  duration = AnimationDuration.Medium,
  direction = 'up',
  distance = 20,
}) => {
  const { fadeStyle } = useFadeInAnimation(delayMs, duration, direction, distance);

  // Get typography style based on variant
  const getTypographyStyle = () => {
    switch (variant) {
      case 'heading1':
        return Typography.Heading1;
      case 'heading2':
        return Typography.Heading2;
      case 'bodyText':
        return Typography.BodyText;
      case 'description':
        return Typography.Description;
      case 'caption':
        return Typography.Caption;
      case 'buttonText':
        return Typography.ButtonText;
      default:
        return Typography.BodyText;
    }
  };

  return (
    <Animated.Text
      style={[
        getTypographyStyle(),
        color && { color },
        fadeStyle,
        style,
      ]}
    >
      {children}
    </Animated.Text>
  );
};

// Animated View Component
interface AnimatedViewProps {
  children: React.ReactNode;
  style?: ViewStyle;
  delayMs?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
}

export const AnimatedView: React.FC<AnimatedViewProps> = ({
  children,
  style,
  delayMs = 0,
  duration = AnimationDuration.Medium,
  direction = 'none',
  distance = 20,
}) => {
  const { fadeStyle } = useFadeInAnimation(delayMs, duration, direction, distance);

  return (
    <Animated.View style={[fadeStyle, style]}>
      {children}
    </Animated.View>
  );
};

// Floating Element Component
interface FloatingElementProps {
  children: React.ReactNode;
  style?: ViewStyle;
  amplitude?: number;
  duration?: number;
}

export const FloatingElement: React.FC<FloatingElementProps> = ({
  children,
  style,
  amplitude = 5,
  duration = AnimationDuration.ExtraLong,
}) => {
  const { floatingStyle } = useFloatingAnimation(amplitude, duration);

  return (
    <Animated.View style={[floatingStyle, style]}>
      {children}
    </Animated.View>
  );
};

// Glow Container Component
interface GlowContainerProps {
  children: React.ReactNode;
  style?: ViewStyle;
  intensity?: number;
  color?: string;
  duration?: number;
  continuous?: boolean;
}

export const GlowContainer: React.FC<GlowContainerProps> = ({
  children,
  style,
  intensity = 0.5,
  color = Colors.DarkText,
  duration = AnimationDuration.Long,
  continuous = true,
}) => {
  const { glowStyle } = useGlowAnimation(intensity, duration);

  // Create platform-specific base shadow style with enhanced Android support
  const baseShadowStyle = {
    shadowColor: color,
    shadowOpacity: Platform.OS === 'android' ? 0.2 : 0.1,
    shadowRadius: Platform.OS === 'android' ? 6 : 4,
    elevation: Platform.OS === 'android' ? 3 : 0,
  };

  return (
    <Animated.View
      style={[
        baseShadowStyle,
        continuous && glowStyle,
        style,
      ]}
    >
      {children}
    </Animated.View>
  );
};
