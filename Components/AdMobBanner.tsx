import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform, Text } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
import { Colors, Spacing } from '../theme';

interface AdMobBannerProps {
  size?: BannerAdSize;
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
  adUnitId?: string; // Allow custom ad unit ID
}

const AdMobBanner: React.FC<AdMobBannerProps> = ({
  size = BannerAdSize.BANNER,
  style,
  onAdLoaded,
  onAdFailedToLoad,
  adUnitId: customAdUnitId,
}) => {
  const [, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [useTestFallback, setUseTestFallback] = useState(false);

  // Use your fresh AdMob ad units - these should work properly
  // 1. ca-app-pub-8809398979690427/1546031594
  // 2. ca-app-pub-8809398979690427/4865511269
  // 3. ca-app-pub-8809398979690427/5120662130
  const productionAdUnitIds = [
    'ca-app-pub-8809398979690427/1546031594', // Ad unit 1
    'ca-app-pub-8809398979690427/4865511269', // Ad unit 2
    'ca-app-pub-8809398979690427/5120662130', // Ad unit 3
  ];

  // Use the first ad unit as primary, with fallback to test ads if production fails
  const primaryAdUnitId = productionAdUnitIds[0];
  const adUnitId = customAdUnitId || (useTestFallback ? TestIds.BANNER : primaryAdUnitId);

  // Debug logging (only in development)
  useEffect(() => {
    if (__DEV__) {
      console.log('🎯 AdMob Banner: Component mounted');
      console.log('🎯 AdMob Banner: Using ad unit ID:', adUnitId);
      console.log('🎯 AdMob Banner: Platform:', Platform.OS);
      console.log('🎯 AdMob Banner: Mode:', useTestFallback ? 'TEST FALLBACK' : 'PRODUCTION');
    }
  }, [adUnitId, useTestFallback]);

  const handleAdLoaded = () => {
    if (__DEV__) console.log('✅ AdMob Banner: Ad loaded successfully');
    setIsLoaded(true);
    setHasError(false);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    if (__DEV__) console.log('❌ AdMob Banner: Failed to load ad:', error);

    // If production ad fails and we haven't tried test fallback yet, try test ads
    if (!useTestFallback && !customAdUnitId) {
      if (__DEV__) console.log('🔄 AdMob Banner: Production ad failed, trying test ad fallback...');
      setUseTestFallback(true);
      setHasError(false);
      return;
    }

    setIsLoaded(false);
    setHasError(true);
    onAdFailedToLoad?.(error);
  };

  // Show debug info if there's an error
  if (hasError) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Text style={styles.debugText}>AdMob Banner: Failed to load</Text>
        <Text style={styles.debugText}>Ad Unit: {adUnitId}</Text>
        <Text style={styles.debugText}>Mode: {useTestFallback ? 'Test Fallback' : 'Production'}</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adUnitId}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          keywords: ['health', 'food', 'cosmetics', 'wellness'],
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: Spacing.Small,
  },
  errorContainer: {
    backgroundColor: Colors.Error,
    padding: Spacing.Small,
  },
  debugText: {
    color: Colors.BackgroundPrimary,
    fontSize: 12,
    textAlign: 'center',
  },
});

export default AdMobBanner;
