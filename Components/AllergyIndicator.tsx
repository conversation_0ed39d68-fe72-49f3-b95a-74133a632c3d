import React, { useState, useMemo } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing } from '../theme';
import Typography from './Typography';
import { usePersonalization } from '../context';

const { width: screenWidth } = Dimensions.get('window');

export type AllergySafetyLevel = 'safe' | 'caution' | 'danger';

interface AllergyMatch {
  userAllergy: string;
  foundIngredients: string[];
  confidence: 'high' | 'medium' | 'low';
}

interface AllergyIndicatorProps {
  productName: string;
  ingredients: Array<{ name: string; description?: string }>;
  apiResponse?: any;
  effects?: {
    positive_effects?: { short_term?: string[]; long_term?: string[] };
    negative_effects?: { short_term?: string[]; long_term?: string[] };
  };
  summary?: string;
  explanationDetails?: string;
}

const AllergyIndicator: React.FC<AllergyIndicatorProps> = ({
  productName,
  ingredients,
  apiResponse,
  effects,
  summary,
  explanationDetails,
}) => {
  const { personalizationState } = usePersonalization();
  const [modalVisible, setModalVisible] = useState(false);

  // Analyze product against user allergies using AI's comprehensive analysis
  const allergyAnalysis = useMemo(() => {
    const userAllergies = personalizationState.allergiesRestrictions;
    if (!userAllergies || userAllergies.length === 0) {
      return {
        level: 'safe' as AllergySafetyLevel,
        matches: [],
        message: 'No allergies specified',
      };
    }

    const matches: AllergyMatch[] = [];

    // Combine all text sources from AI analysis for comprehensive checking
    const allAnalysisText = [
      summary || '',
      explanationDetails || '',
      ...(effects?.positive_effects?.short_term || []),
      ...(effects?.positive_effects?.long_term || []),
      ...(effects?.negative_effects?.short_term || []),
      ...(effects?.negative_effects?.long_term || []),
      ...ingredients.map(ing => `${ing.name} ${ing.description || ''}`),
      // Also check API response raw data
      JSON.stringify(apiResponse || {})
    ].join(' ').toLowerCase();

    // Check each user allergy against the comprehensive AI analysis
    userAllergies.forEach(allergy => {
      const allergyLower = allergy.toLowerCase();
      const foundSources: string[] = [];
      let confidence: 'high' | 'medium' | 'low' = 'low';

      // Define search terms for each allergy type
      let searchTerms: string[] = [allergyLower];

      if (allergyLower === 'gluten') {
        searchTerms = ['gluten', 'wheat', 'barley', 'rye', 'flour', 'bread', 'pasta', 'cereal'];
      } else if (allergyLower === 'dairy' || allergyLower === 'milk') {
        searchTerms = ['dairy', 'milk', 'cream', 'butter', 'cheese', 'whey', 'casein', 'lactose', 'yogurt'];
      } else if (allergyLower === 'peanuts' || allergyLower === 'peanut') {
        searchTerms = ['peanut', 'groundnut'];
      } else if (allergyLower === 'tree nuts') {
        searchTerms = ['almond', 'walnut', 'cashew', 'pecan', 'hazelnut', 'brazil nut', 'macadamia', 'pistachio'];
      } else if (allergyLower === 'soy') {
        searchTerms = ['soy', 'soya', 'soybean', 'tofu', 'tempeh', 'miso'];
      } else if (allergyLower === 'shellfish') {
        searchTerms = ['shellfish', 'shrimp', 'crab', 'lobster', 'oyster', 'mussel', 'clam'];
      } else if (allergyLower === 'fish') {
        searchTerms = ['fish', 'salmon', 'tuna', 'cod', 'sardine', 'anchovy'];
      } else if (allergyLower === 'eggs' || allergyLower === 'egg') {
        searchTerms = ['egg', 'albumin', 'lecithin'];
      } else if (allergyLower === 'vegan') {
        searchTerms = ['milk', 'cream', 'butter', 'cheese', 'whey', 'casein', 'lactose', 'honey', 'gelatin', 'egg', 'meat', 'fish', 'chicken', 'beef', 'pork'];
      } else if (allergyLower === 'vegetarian') {
        searchTerms = ['meat', 'fish', 'chicken', 'beef', 'pork', 'gelatin', 'lard', 'tallow'];
      }

      // Check if any search terms are found in the AI analysis
      let foundTerms: string[] = [];
      searchTerms.forEach(term => {
        if (allAnalysisText.includes(term)) {
          foundTerms.push(term);
          confidence = 'high'; // AI mentioned it, so high confidence
        }
      });

      // Also check specific effects mentions
      const allEffects = [
        ...(effects?.positive_effects?.short_term || []),
        ...(effects?.positive_effects?.long_term || []),
        ...(effects?.negative_effects?.short_term || []),
        ...(effects?.negative_effects?.long_term || [])
      ];

      allEffects.forEach(effect => {
        const effectLower = effect.toLowerCase();
        searchTerms.forEach(term => {
          if (effectLower.includes(term)) {
            foundSources.push(`Effects: ${effect}`);
            confidence = 'high';
          }
        });
      });

      // Check ingredients specifically
      ingredients.forEach(ingredient => {
        const ingredientText = `${ingredient.name} ${ingredient.description || ''}`.toLowerCase();
        searchTerms.forEach(term => {
          if (ingredientText.includes(term)) {
            foundSources.push(`Ingredient: ${ingredient.name}`);
            confidence = 'high';
          }
        });
      });

      // If we found matches, add to results
      if (foundTerms.length > 0 || foundSources.length > 0) {
        matches.push({
          userAllergy: allergy,
          foundIngredients: foundSources.length > 0 ? foundSources : foundTerms,
          confidence,
        });
      }
    });

    // Determine safety level based on AI analysis
    let level: AllergySafetyLevel = 'safe';
    let message = 'No known allergens detected';

    if (matches.length > 0) {
      const hasHighConfidenceMatches = matches.some(match => match.confidence === 'high');
      if (hasHighConfidenceMatches) {
        level = 'danger';
        message = `AI detected ${matches.length} allergen${matches.length > 1 ? 's' : ''}`;
      } else {
        level = 'caution';
        message = 'Potential allergens detected by AI';
      }
    }

    return { level, matches, message };
  }, [personalizationState.allergiesRestrictions, ingredients, effects, summary, explanationDetails, apiResponse]);

  // Get icon and color based on safety level
  const getIndicatorStyle = () => {
    switch (allergyAnalysis.level) {
      case 'safe':
        return {
          backgroundColor: Colors.Success,
          iconName: 'checkmark-circle' as const,
          iconColor: Colors.BackgroundPrimary,
        };
      case 'caution':
        return {
          backgroundColor: Colors.Warning,
          iconName: 'warning' as const,
          iconColor: Colors.BackgroundPrimary,
        };
      case 'danger':
        return {
          backgroundColor: Colors.Error,
          iconName: 'close-circle' as const,
          iconColor: Colors.BackgroundPrimary,
        };
    }
  };

  const indicatorStyle = getIndicatorStyle();

  // Don't render if no allergies are set
  if (!personalizationState.allergiesRestrictions || personalizationState.allergiesRestrictions.length === 0) {
    return null;
  }

  return (
    <>
      <TouchableOpacity
        style={[styles.indicator, { backgroundColor: indicatorStyle.backgroundColor }]}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.8}
      >
        <Ionicons
          name={indicatorStyle.iconName}
          size={20}
          color={indicatorStyle.iconColor}
        />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <View style={styles.modalTitleContainer}>
                <View style={[styles.modalIndicator, { backgroundColor: indicatorStyle.backgroundColor }]}>
                  <Ionicons
                    name={indicatorStyle.iconName}
                    size={16}
                    color={indicatorStyle.iconColor}
                  />
                </View>
                <Typography variant="heading3" style={styles.modalTitle}>
                  Allergy Safety
                </Typography>
              </View>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={Colors.DarkText} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              <Typography variant="bodyText" style={styles.modalMessage}>
                {allergyAnalysis.level === 'safe'
                  ? `✅ Safe for your dietary preferences. No conflicts detected with your ${personalizationState.allergiesRestrictions.length} restriction${personalizationState.allergiesRestrictions.length > 1 ? 's' : ''}.`
                  : allergyAnalysis.level === 'danger'
                  ? `⚠️ Contains allergens that conflict with your dietary restrictions. Review details below.`
                  : `⚠️ Potential allergen concerns detected. Please review the analysis below.`
                }
              </Typography>

              {allergyAnalysis.matches.length > 0 && (
                <View style={styles.matchesContainer}>
                  <Typography variant="heading4" style={styles.matchesTitle}>
                    Detected Allergens:
                  </Typography>
                  {allergyAnalysis.matches.map((match, index) => (
                    <View key={index} style={styles.matchItem}>
                      <View style={styles.matchHeader}>
                        <Typography variant="bodyText" style={styles.allergyName}>
                          {match.userAllergy}
                        </Typography>
                        <View style={[
                          styles.confidenceBadge,
                          { backgroundColor: match.confidence === 'high' ? Colors.Error : Colors.Warning }
                        ]}>
                          <Typography variant="description" style={styles.confidenceText}>
                            {match.confidence}
                          </Typography>
                        </View>
                      </View>
                      <Typography variant="description" style={styles.foundIngredients}>
                        Found in: {match.foundIngredients.join(', ')}
                      </Typography>
                    </View>
                  ))}
                </View>
              )}

              <View style={styles.userAllergiesContainer}>
                <Typography variant="heading4" style={styles.userAllergiesTitle}>
                  Your Allergies & Restrictions:
                </Typography>
                <View style={styles.userAllergiesList}>
                  {personalizationState.allergiesRestrictions.map((allergy, index) => (
                    <View key={index} style={styles.userAllergyTag}>
                      <Typography variant="description" style={styles.userAllergyText}>
                        {allergy}
                      </Typography>
                    </View>
                  ))}
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  indicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
  },
  modalContent: {
    backgroundColor: Colors.BackgroundPrimary,
    borderRadius: 16,
    width: '100%',
    maxWidth: screenWidth - (Spacing.Large * 2),
    maxHeight: '80%',
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.Large,
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
  },
  modalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Small,
  },
  modalTitle: {
    fontWeight: '600',
  },
  closeButton: {
    padding: Spacing.ExtraSmall,
  },
  modalBody: {
    padding: Spacing.Large,
  },
  modalMessage: {
    marginBottom: Spacing.Large,
    textAlign: 'center',
    fontWeight: '500',
  },
  matchesContainer: {
    marginBottom: Spacing.Large,
  },
  matchesTitle: {
    marginBottom: Spacing.Medium,
    fontWeight: '600',
    color: Colors.Error,
  },
  matchItem: {
    backgroundColor: 'rgba(255, 59, 48, 0.05)',
    padding: Spacing.Medium,
    borderRadius: 8,
    marginBottom: Spacing.Small,
  },
  matchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.ExtraSmall,
  },
  allergyName: {
    fontWeight: '600',
    color: Colors.Error,
  },
  confidenceBadge: {
    paddingHorizontal: Spacing.ExtraSmall,
    paddingVertical: 2,
    borderRadius: 4,
  },
  confidenceText: {
    color: Colors.BackgroundPrimary,
    fontWeight: '500',
    fontSize: 10,
  },
  foundIngredients: {
    color: Colors.LightText,
  },
  userAllergiesContainer: {
    marginTop: Spacing.Medium,
  },
  userAllergiesTitle: {
    marginBottom: Spacing.Medium,
    fontWeight: '600',
  },
  userAllergiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  userAllergyTag: {
    backgroundColor: Colors.SurfaceSecondary,
    paddingHorizontal: Spacing.Small,
    paddingVertical: Spacing.ExtraSmall,
    borderRadius: 12,
    marginRight: Spacing.ExtraSmall,
    marginBottom: Spacing.ExtraSmall,
  },
  userAllergyText: {
    color: Colors.DarkText,
  },
});

export default AllergyIndicator;
