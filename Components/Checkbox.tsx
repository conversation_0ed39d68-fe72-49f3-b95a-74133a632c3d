import React from 'react';
import { TouchableOpacity, View, StyleSheet, ViewStyle } from 'react-native';
import { Colors } from '../theme';
import { Ionicons } from '@expo/vector-icons';

interface CheckboxProps {
  checked: boolean;
  onPress: () => void;
  style?: ViewStyle;
  size?: number;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onPress,
  style,
  size = 24,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.container,
        { width: size, height: size },
        checked ? styles.checked : styles.unchecked,
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.85} // Premium opacity change
    >
      {checked && (
        <Ionicons
          name="checkmark"
          size={size * 0.7}
          color={Colors.BackgroundPrimary}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4, // Half of small border radius
  },
  checked: {
    backgroundColor: Colors.DarkText,
    borderWidth: 0,
  },
  unchecked: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Colors.LightText,
  },
});

export default Checkbox;
