import React from 'react';
import { TouchableOpacity, View, StyleSheet, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../theme';
import GlowingElement from './GlowingElement';

interface GlowingCaptureButtonProps {
  onPress: () => void;
  style?: ViewStyle;
  size?: number;
  iconSize?: number;
  glowIntensity?: number;
}

const GlowingCaptureButton: React.FC<GlowingCaptureButtonProps> = ({
  onPress,
  style,
  size = 70,
  iconSize = 32,
  glowIntensity = 0.7,
}) => {
  return (
    <GlowingElement
      color={Colors.BackgroundPrimary}
      intensity={glowIntensity}
      active={true}
      size={size}
      style={[styles.container, style]}
    >
      <TouchableOpacity
        style={[
          styles.button,
          { width: size, height: size, borderRadius: size / 2 }
        ]}
        onPress={onPress}
        activeOpacity={0.85} // Premium opacity change
      >
        <View style={[
          styles.innerButton,
          { width: size * 0.8, height: size * 0.8, borderRadius: size * 0.4 }
        ]}>
          <Ionicons name="camera" size={iconSize} color={Colors.DarkText} />
        </View>
      </TouchableOpacity>
    </GlowingElement>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.BackgroundPrimary,
  },
  innerButton: {
    backgroundColor: Colors.BackgroundPrimary,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default GlowingCaptureButton;
