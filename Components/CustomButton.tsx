import React, { ReactNode } from "react";
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, View } from "react-native";
import { Colors, Typography, Spacing, Shadow } from "../theme";

interface CustomButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'tertiary';
  style?: ViewStyle;
  textStyle?: TextStyle;
  disabled?: boolean;
  icon?: ReactNode;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  style,
  textStyle,
  disabled = false,
  icon,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.button,
        variant === 'primary' && styles.primaryButton,
        variant === 'secondary' && styles.secondaryButton,
        variant === 'tertiary' && styles.tertiaryButton,
        disabled && styles.disabledButton,
        (variant === 'primary' || variant === 'secondary') && Shadow.Small,
        style,
      ]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.85} // Premium opacity change instead of scale
    >
      <View style={styles.buttonContent}>
        {icon && <View style={styles.iconContainer}>{icon}</View>}
        <Text
          style={[
            styles.buttonText,
            variant === 'primary' && styles.primaryButtonText,
            variant === 'secondary' && styles.secondaryButtonText,
            variant === 'tertiary' && styles.tertiaryButtonText,
            disabled && styles.disabledButtonText,
            textStyle,
          ]}
        >
          {title}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
    borderRadius: 12, // Medium border radius
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginRight: Spacing.Small,
  },
  primaryButton: {
    backgroundColor: Colors.DarkText,
  },
  secondaryButton: {
    backgroundColor: Colors.SurfaceSecondary,
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
  },
  disabledButton: {
    backgroundColor: Colors.LightText,
    opacity: 0.7,
  },
  buttonText: {
    ...Typography.ButtonText,
    textAlign: 'center',
  },
  primaryButtonText: {
    color: Colors.BackgroundPrimary,
  },
  secondaryButtonText: {
    color: Colors.DarkText,
  },
  tertiaryButtonText: {
    color: Colors.AccentBlue,
  },
  disabledButtonText: {
    color: Colors.SurfaceSecondary,
  },
});

export default CustomButton;
