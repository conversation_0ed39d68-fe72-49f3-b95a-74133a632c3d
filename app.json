{"expo": {"name": "JunkChk", "slug": "JunkChk", "version": "0.2.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "icon": "./assets/ios-icons/180.png", "bundleIdentifier": "com.akhilkirank.JunkChk", "googleMobileAdsAppId": "ca-app-pub-8809398979690427~**********", "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}, "CADisableMinimumFrameDurationOnPhone": true, "UIViewControllerBasedStatusBarAppearance": false, "GADApplicationIdentifier": "ca-app-pub-8809398979690427~**********", "NSCameraUsageDescription": "This app uses the camera to scan product barcodes and take photos of product labels for health analysis.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to analyze product images for health information.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": "cstr6suwn9.skadnetwork"}, {"SKAdNetworkIdentifier": "4fzdc2evr5.skadnetwork"}, {"SKAdNetworkIdentifier": "4pfyvq9l8r.skadnetwork"}, {"SKAdNetworkIdentifier": "2fnua5tdw4.skadnetwork"}, {"SKAdNetworkIdentifier": "ydx93a7ass.skadnetwork"}, {"SKAdNetworkIdentifier": "5a6flpkh64.skadnetwork"}]}}, "android": {"icon": "./assets/adaptive-icon.png", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.akhilkirank.JunkChk", "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"], "navigationBar": {"visible": false, "backgroundColor": "#ffffff"}, "softwareKeyboardLayoutMode": "pan", "allowBackup": false}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "e795983f-3818-436b-9aae-7e5b4e2badc0"}}, "fonts": [{"asset": "./assets/fonts/Poppins-Regular.ttf", "family": "<PERSON><PERSON><PERSON>"}, {"asset": "./assets/fonts/Poppins-Medium.ttf", "family": "Poppins-Medium"}, {"asset": "./assets/fonts/Poppins-SemiBold.ttf", "family": "Poppins-SemiBold"}, {"asset": "./assets/fonts/Poppins-Bold.ttf", "family": "Poppins-Bold"}, {"asset": "./assets/fonts/Poppins-ExtraBold.ttf", "family": "Poppins-ExtraBold"}, {"asset": "./assets/fonts/Poppins-Black.ttf", "family": "Poppins-<PERSON>"}], "plugins": ["expo-secure-store", "expo-dev-client", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}], ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-8809398979690427~**********", "iosAppId": "ca-app-pub-8809398979690427~**********"}]]}}