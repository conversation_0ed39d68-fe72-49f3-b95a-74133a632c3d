import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSequence,
  withSpring,
  withRepeat,
  Easing,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { AnimatedText, AnimatedView, AnimatedButton } from '../Components/AnimatedComponents';
import { AnimationDuration, AnimationEasing } from '../Components/animations';
import AnimatedPersonalizationHeader from '../Components/AnimatedPersonalizationHeader';
import Card from '../Components/Card';
import Typography from '../Components/Typography';
import { usePersonalization, Goal } from '../context';

type RootStackParamList = {
  YourGoals: undefined;
  StartPersonalization: undefined;
  AllergiesRestrictions: undefined;
};

type YourGoalsScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'YourGoals'
>;

const goals: Goal[] = [
  'To identify allergens/intolerances',
  'To understand ingredient lists',
  'To find healthier options',
  'To track product information',
  'Other',
];

// -------------------------------------------
// GoalItem sub-component (defined outside main component to obey Hook rules)
// -------------------------------------------
interface GoalItemProps {
  goal: Goal;
  selectedGoal: Goal | null;
  onSelect: (goal: Goal) => void;
  anim: Animated.SharedValue<number>;
  index?: number; // optional, for key only
}

const GoalItem: React.FC<GoalItemProps> = ({ goal, selectedGoal, onSelect, anim }) => {
  const animatedStyle = useAnimatedStyle(() => ({
    opacity: anim.value,
    transform: [
      { translateX: interpolate(anim.value, [0, 1], [20, 0]) },
      { scale: interpolate(anim.value, [0, 1], [0.95, 1]) },
    ] as any,
  }));

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        style={[
          styles.goalItem,
          selectedGoal === goal && styles.selectedGoalItem,
        ]}
        onPress={() => onSelect(goal)}
      >
        <Typography
          variant="bodyText"
          color={selectedGoal === goal ? Colors.BackgroundPrimary : Colors.DarkText}
        >
          {goal}
        </Typography>
      </TouchableOpacity>
    </Animated.View>
  );
};

// -------------------------------------------
// Main screen component
// -------------------------------------------
const YourGoalsScreen: React.FC = () => {
  const navigation = useNavigation<YourGoalsScreenNavigationProp>();
  const { personalizationState, setGoal } = usePersonalization();
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(personalizationState.goal);

  // Sync local state when personalization state changes (e.g., when data is reset)
  useEffect(() => {
    setSelectedGoal(personalizationState.goal);
  }, [personalizationState.goal]);

  // Debug logging
  console.log('YourGoalsScreen: Component mounted');
  console.log('YourGoalsScreen: personalizationState:', personalizationState);

  // Animation values - keep stable across re-renders
  const contentOpacity = useSharedValue(0);
  const goalItemAnimations = goals.map(() => useSharedValue(0));
  const buttonScale = useSharedValue(0.95);
  const buttonOpacity = useSharedValue(0);

  // Background animation value
  const backgroundAnim = useSharedValue(0);

  useEffect(() => {
    try {
      console.log('YourGoalsScreen: Starting animations');

      // Animate content with error handling
      try {
        contentOpacity.value = withDelay(
          300, // Slightly longer delay to let the header animate first
          withTiming(1, {
            duration: AnimationDuration.Medium,
            easing: AnimationEasing.Smooth
          })
        );
      } catch (animError) {
        console.error('YourGoalsScreen: Error animating content:', animError);
      }

      // Animate goal items sequentially with error handling
      try {
        goalItemAnimations.forEach((anim, index) => {
          anim.value = withDelay(
            400 + (index * 100),
            withTiming(1, {
              duration: AnimationDuration.Medium,
              easing: AnimationEasing.Smooth
            })
          );
        });
      } catch (animError) {
        console.error('YourGoalsScreen: Error animating goal items:', animError);
      }

      // Animate button with error handling
      try {
        buttonOpacity.value = withDelay(
          400 + (goals.length * 100),
          withTiming(1, {
            duration: AnimationDuration.Medium,
            easing: AnimationEasing.Smooth
          })
        );
      } catch (animError) {
        console.error('YourGoalsScreen: Error animating button:', animError);
      }

      // Simplified background animation - no infinite repeat to prevent memory issues
      try {
        backgroundAnim.value = withTiming(1, {
          duration: 2000,
          easing: Easing.inOut(Easing.ease)
        });
      } catch (animError) {
        console.error('YourGoalsScreen: Error animating background:', animError);
      }

      return () => {
        try {
          // Cleanup animations safely
          contentOpacity.value = 0;
          goalItemAnimations.forEach((anim, index) => {
            try {
              anim.value = 0;
            } catch (cleanupError) {
              console.error(`YourGoalsScreen: Error cleaning up goal animation ${index}:`, cleanupError);
            }
          });
          buttonOpacity.value = 0;
          buttonScale.value = 0.95;
          backgroundAnim.value = 0;
        } catch (cleanupError) {
          console.error('YourGoalsScreen: Error during cleanup:', cleanupError);
        }
      };
    } catch (error) {
      console.error('YourGoalsScreen: Error setting up animations:', error);
    }
  }, []);

  // Animated styles

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value,
    transform: [
      { translateY: interpolate(contentOpacity.value, [0, 1], [20, 0]) }
    ]
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: buttonOpacity.value,
    // Removed scale transform for premium feel
  }));

  const backgroundAnimatedStyle = useAnimatedStyle(() => {
    try {
      return {
        opacity: 0.05 * backgroundAnim.value,
        transform: [
          { scale: 1 + (backgroundAnim.value * 0.05) }, // Reduced scale effect
        ],
      };
    } catch (error) {
      console.error('YourGoalsScreen: Error in backgroundAnimatedStyle:', error);
      return {
        opacity: 0.05,
        transform: [{ scale: 1 }],
      };
    }
  });

  // Goal item selection animation
  const handleGoalSelect = async (goal: Goal) => {
    try {
      // Animate button scale when a goal is selected
      if (!selectedGoal) {
        try {
          buttonScale.value = withSequence(
            withTiming(1.05, { duration: 150, easing: AnimationEasing.Bounce }),
            withTiming(1, { duration: 150, easing: AnimationEasing.Bounce })
          );
        } catch (animError) {
          console.error('YourGoalsScreen: Error animating button scale:', animError);
        }
      }

      setSelectedGoal(goal);
      await setGoal(goal);
    } catch (error) {
      console.error('YourGoalsScreen: Error selecting goal:', error);
    }
  };

  const handleBack = () => {
    try {
      console.log('YourGoalsScreen: Navigating back to StartPersonalization');
      navigation.navigate('StartPersonalization');
    } catch (error) {
      console.error('YourGoalsScreen: Error navigating back:', error);
    }
  };

  const handleNext = async () => {
    try {
      console.log('YourGoalsScreen: handleNext called with selectedGoal:', selectedGoal);
      if (selectedGoal) {
        await setGoal(selectedGoal);
        console.log('YourGoalsScreen: Goal set successfully, navigating to AllergiesRestrictions');
        navigation.navigate('AllergiesRestrictions');
      } else {
        console.warn('YourGoalsScreen: No goal selected');
      }
    } catch (error) {
      console.error('YourGoalsScreen: Error in handleNext:', error);
    }
  };

  return (
    <View style={styles.container}>
      {/* Animated background element */}
      <Animated.View style={[styles.backgroundElement, backgroundAnimatedStyle]} />

      <AnimatedPersonalizationHeader
        currentStep={1}
        totalSteps={3}
        onBack={handleBack}
        animationDelay={100}
      />

      <View style={styles.screenContainer}>
        <Animated.View style={[styles.content, contentAnimatedStyle]}>
          <AnimatedText
            variant="heading2"
            style={styles.title}
            delayMs={200}
            duration={600}
            direction="up"
          >
            Why are you using JunkChk?
          </AnimatedText>

          <AnimatedText
            variant="bodyText"
            style={styles.description}
            delayMs={300}
            duration={600}
            direction="up"
          >
            Select the main reason below:
          </AnimatedText>

          <View style={styles.goalsContainer}>
            {goals.map((goal, index) => (
              <GoalItem key={index} goal={goal} selectedGoal={selectedGoal} onSelect={handleGoalSelect} anim={goalItemAnimations[index]} />
            ))}
          </View>
        </Animated.View>

        <Animated.View style={[styles.footer, buttonAnimatedStyle]}>
          <AnimatedButton
            title="Next"
            onPress={handleNext}
            variant="primary"
            style={styles.button}
            disabled={!selectedGoal}
            glowOnPress={true}
            glowIntensity={0.4}
            scaleOnPress={true}
          />
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
    overflow: 'hidden',
  },
  backgroundElement: {
    position: 'absolute',
    top: -200,
    right: -150,
    width: 400,
    height: 400,
    borderRadius: 200,
    backgroundColor: Colors.DarkText,
    opacity: 0.05,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
  },
  title: {
    marginBottom: Spacing.Medium,
  },
  description: {
    marginBottom: Spacing.Large,
    color: Colors.LightText,
  },
  goalsContainer: {
    marginTop: Spacing.Medium,
  },
  goalItem: {
    backgroundColor: Colors.SurfaceSecondary,
    borderRadius: 12, // Medium border radius
    padding: Spacing.Medium,
    marginBottom: Spacing.Medium,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  selectedGoalItem: {
    backgroundColor: Colors.DarkText,
    shadowColor: Colors.DarkText,
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 4,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
  },
  button: {
    width: '100%',
  },
});

export default YourGoalsScreen;
