import React, { useEffect } from 'react';
import { View, StyleSheet, Text, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing } from '../theme';
import Screen from '../Components/Screen';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withRepeat,
  withSequence,
  Easing,
} from 'react-native-reanimated';
import { AnimatedText, AnimatedView, FloatingElement, AnimatedButton, GlowContainer } from '../Components/AnimatedComponents';
import { AnimationDuration } from '../Components/animations';

type RootStackParamList = {
  Welcome: undefined;
  StartPersonalization: undefined;
};

type WelcomeScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Welcome'
>;

const WelcomeScreen: React.FC = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();

  // Background animation values
  const backgroundAnim = useSharedValue(0);

  // Start background animation on mount
  useEffect(() => {
    backgroundAnim.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 10000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0, { duration: 10000, easing: Easing.inOut(Easing.ease) })
      ),
      -1, // Infinite repeat
      true // Reverse
    );

    return () => {
      // Cleanup
      backgroundAnim.value = 0;
    };
  }, []);

  // Animated background style
  const animatedBackgroundStyle = useAnimatedStyle(() => {
    return {
      opacity: 0.05,
      transform: [
        { scale: 1 + (backgroundAnim.value * 0.05) },
        { rotate: `${backgroundAnim.value * 5}deg` },
      ],
    };
  });

  const handleGetStarted = () => {
    navigation.navigate('StartPersonalization');
  };

  return (
    <Screen style={styles.container}>
      {/* Animated background element */}
      <Animated.View style={[styles.backgroundElement, animatedBackgroundStyle]} />

      <View style={styles.content}>
        {/* Animated logo with floating effect */}
        <FloatingElement amplitude={3} duration={3000} style={styles.logoContainer}>
          <GlowContainer intensity={0.3} color={Colors.DarkText} style={styles.logoInner}>
            <Text style={styles.logoText}>JC</Text>
          </GlowContainer>
        </FloatingElement>

        {/* Animated title with fade-in effect */}
        <AnimatedText
          variant="heading1"
          style={styles.title}
          delayMs={300}
          duration={800}
          direction="up"
        >
          JunkChk
        </AnimatedText>

        {/* Animated tagline with fade-in effect */}
        <AnimatedText
          variant="bodyText"
          style={styles.tagline}
          delayMs={600}
          duration={800}
          direction="up"
        >
          Your smart health companion for food and cosmetics
        </AnimatedText>
      </View>

      {/* Animated footer with button */}
      <AnimatedView
        style={styles.footer}
        delayMs={900}
        duration={800}
        direction="up"
      >
        <AnimatedButton
          title="Get Started"
          onPress={handleGetStarted}
          variant="primary"
          style={styles.button}
          glowOnPress={true}
          glowIntensity={0.5}
          glowColor={Colors.DarkText}
          premiumAnimation={true}
          animationIntensity={1}
        />
      </AnimatedView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
    overflow: 'hidden',
  },
  backgroundElement: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: Colors.DarkText,
    opacity: 0.05,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.Large,
  },
  logoContainer: {
    marginBottom: Spacing.Large,
  },
  logoInner: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.DarkText,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    fontSize: 48,
    fontFamily: 'Poppins-Bold',
    color: Colors.BackgroundPrimary,
    // Text shadow properties - only apply on iOS
    ...(Platform.OS === 'ios' ? {
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 2,
    } : {}),
  },
  title: {
    textAlign: 'center',
    marginBottom: Spacing.Medium,
  },
  tagline: {
    textAlign: 'center',
    marginBottom: Spacing.ExtraLarge,
    color: Colors.LightText,
  },
  footer: {
    paddingHorizontal: Spacing.Large,
    paddingBottom: Spacing.ExtraLarge,
  },
  button: {
    width: '100%',
  },
});

export default WelcomeScreen;
