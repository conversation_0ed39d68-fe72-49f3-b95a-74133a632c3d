import 'react-native-gesture-handler';
import { StatusBar } from "expo-status-bar";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { useCallback, useEffect } from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { getScreenOptions } from "./Components/NavigationAnimations";
import { AppProvider } from "./context";
import AuthNavigator from "./navigation/AuthNavigator";
import { useHideNavigationBar } from "./hooks/useNavigationBar";
import AdMobService from "./services/AdMobService";
import ErrorBoundary from "./Components/ErrorBoundary";
import PerformanceMonitor from "./Components/PerformanceMonitor";
import CrashPreventionSystem from "./utils/CrashPrevention";

// Import screens directly to avoid lazy loading issues during debugging
import HomeScreen from "./Pages/HomeScreen";
import WelcomeScreen from "./Pages/WelcomeScreen";
import StartPersonalizationScreen from "./Pages/StartPersonalizationScreen";
import YourGoalsScreen from "./Pages/YourGoalsScreen";
import AllergiesRestrictionsScreen from "./Pages/AllergiesRestrictionsScreen";
import HowYourAnswersHelpScreen from "./Pages/HowYourAnswersHelpScreen";
import HowJunkChkWorksScreen from "./Pages/HowJunkChkWorksScreen";
import ScanScreen from "./Pages/ScanScreen";
import ProcessingScreen from "./Pages/ProcessingScreen";
import ScanResultsScreen from "./Pages/ScanResultsScreen";
import ScanErrorScreen from "./Pages/ScanErrorScreen";
import TermsPrivacyScreen from "./Pages/TermsPrivacyScreen";
import PhoneNumberLoginScreen from "./Pages/PhoneNumberLoginScreen";
import OTPVerificationScreen from "./Pages/OTPVerificationScreen";
import CreatingAccountScreen from "./Pages/CreatingAccountScreen";
import AccountCreatedScreen from "./Pages/AccountCreatedScreen";
import HistoryScreen from "./Pages/HistoryScreen";
import ComparisonSelectionScreen from "./Pages/ComparisonSelectionScreen";
import ComparisonViewScreen from "./Pages/ComparisonViewScreen";
import SettingsScreen from "./Pages/SettingsScreen";
import SettingsAccountScreen from "./Pages/SettingsAccountScreen";
import SettingsAllergiesScreen from "./Pages/SettingsAllergiesScreen";
import SettingsSubscriptionScreen from "./Pages/SettingsSubscriptionScreen";
import SettingsSupportScreen from "./Pages/SettingsSupportScreen";
import SettingsAboutScreen from "./Pages/SettingsAboutScreen";
// ServerTestScreen removed - was causing lazy loading issues

// Prevent the splash screen from automatically hiding
SplashScreen.preventAutoHideAsync().catch((error) => {
  console.log('SplashScreen.preventAutoHideAsync error (safe to ignore):', error.message);
});

// LoadingFallback removed - no longer using lazy loading

// Optimized font loading - only load essential fonts initially
const fontConfig = {
  "Poppins": require("./assets/fonts/Poppins-Regular.ttf"),
  "Poppins-Medium": require("./assets/fonts/Poppins-Medium.ttf"),
  "Poppins-SemiBold": require("./assets/fonts/Poppins-SemiBold.ttf"),
  "Poppins-Bold": require("./assets/fonts/Poppins-Bold.ttf"),
  "Poppins-ExtraBold": require("./assets/fonts/Poppins-ExtraBold.ttf"),
  "Poppins-Black": require("./assets/fonts/Poppins-Black.ttf"),
};

const Stack = createNativeStackNavigator();
export default function App() {
  // Hide Android navigation bar
  useHideNavigationBar();

  const [fontsLoaded] = useFonts(fontConfig);

  // Initialize crash prevention system
  useEffect(() => {
    const initializeCrashPrevention = async () => {
      try {
        await CrashPreventionSystem.initialize();
        console.log('App: Crash prevention system initialized');
      } catch (error) {
        console.error('App: Failed to initialize crash prevention:', error);
      }
    };

    initializeCrashPrevention();

    // Initialize AdMob
    const initializeAdMob = async () => {
      try {
        await AdMobService.getInstance().initialize();
      } catch (error) {
        console.error('Failed to initialize AdMob:', error);
      }
    };

    initializeAdMob();

    // Cleanup on unmount
    return () => {
      CrashPreventionSystem.cleanup();
    };
  }, []);

  const onLayoutRootView = useCallback(async () => {
    if (fontsLoaded) {
      try {
        // Hide the splash screen once fonts are loaded
        await SplashScreen.hideAsync();
      } catch (error) {
        console.log('SplashScreen.hideAsync error (safe to ignore):', error.message);
      }
    }
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <ErrorBoundary>
      <PerformanceMonitor componentName="App">
        <SafeAreaProvider>
          <AppProvider>
            <NavigationContainer onReady={onLayoutRootView}>
              <Stack.Navigator
                initialRouteName="AuthNavigator"
                screenOptions={getScreenOptions('ios_like')}
              >
              {/* Auth Navigator - Initial screen that checks auth state */}
              <Stack.Screen
                name="AuthNavigator"
                component={AuthNavigator}
                options={{ headerShown: false }}
              />

              {/* Main app screens */}
              <Stack.Screen name="Home" component={HomeScreen} />
              <Stack.Screen name="History" component={HistoryScreen} />
              <Stack.Screen name="ComparisonSelection" component={ComparisonSelectionScreen} />
              <Stack.Screen name="ComparisonView" component={ComparisonViewScreen} />
              <Stack.Screen name="Settings" component={SettingsScreen} />
              <Stack.Screen name="SettingsAccount" component={SettingsAccountScreen} />
              <Stack.Screen name="SettingsAllergies" component={SettingsAllergiesScreen} />
              <Stack.Screen name="SettingsSubscription" component={SettingsSubscriptionScreen} />
              <Stack.Screen name="SettingsSupport" component={SettingsSupportScreen} />
              <Stack.Screen name="SettingsAbout" component={SettingsAboutScreen} />

              {/* Debug/Test screens - ServerTest removed due to import issues */}

              {/* Core functionality screens */}
              <Stack.Screen name="Scan" component={ScanScreen} />
              <Stack.Screen
                name="Processing"
                component={ProcessingScreen}
                options={{ gestureEnabled: false }}
              />
              <Stack.Screen
                name="ScanResults"
                component={ScanResultsScreen}
                options={{ gestureEnabled: false }}
              />
              <Stack.Screen name="ScanError" component={ScanErrorScreen} />

              {/* Onboarding screens */}
              <Stack.Screen name="Welcome" component={WelcomeScreen} />
              <Stack.Screen name="StartPersonalization" component={StartPersonalizationScreen} />
              <Stack.Screen name="YourGoals" component={YourGoalsScreen} />
              <Stack.Screen name="AllergiesRestrictions" component={AllergiesRestrictionsScreen} />
              <Stack.Screen name="HowYourAnswersHelp" component={HowYourAnswersHelpScreen} />
              <Stack.Screen name="HowJunkChkWorks" component={HowJunkChkWorksScreen} />

              {/* Account creation screens */}
              <Stack.Screen name="TermsPrivacy" component={TermsPrivacyScreen} />
              <Stack.Screen name="PhoneNumberLogin" component={PhoneNumberLoginScreen} />
              <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />
              <Stack.Screen
                name="CreatingAccount"
                component={CreatingAccountScreen}
                options={{ gestureEnabled: false }}
              />
              <Stack.Screen
                name="AccountCreated"
                component={AccountCreatedScreen}
                options={{ gestureEnabled: false }}
              />
              </Stack.Navigator>
            </NavigationContainer>
          </AppProvider>
        </SafeAreaProvider>
      </PerformanceMonitor>
    </ErrorBoundary>
  );
}

// Styles removed - no longer needed without lazy loading
