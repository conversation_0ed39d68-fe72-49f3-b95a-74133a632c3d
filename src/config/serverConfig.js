/**
 * Server Configuration
 * Centralized configuration for server endpoints
 */

// Environment detection
const isDevelopment = __DEV__;

// Platform detection for proper server URL
const getDevelopmentServerUrl = () => {
  if (!isDevelopment) {
    return 'https://your-production-server.com'; // TODO: Update with actual production URL
  }

  // For development, use localhost for iOS simulator and emulator
  // This ensures proper connectivity from simulator to local server
  // TODO: Update this IP address to match your development environment
  return 'http://localhost:3000'; // Changed from hardcoded IP to localhost
};

// Server configurations for different environments
const ENVIRONMENTS = {
  development: {
    baseUrl: getDevelopmentServerUrl(),
    timeout: 60000,
    retries: 3, // Increased retries for development
  },
  production: {
    baseUrl: 'https://your-production-server.com', // Update with your production URL
    timeout: 60000,
    retries: 3
  }
};

// Get current environment config
const getCurrentConfig = () => {
  return isDevelopment ? ENVIRONMENTS.development : ENVIRONMENTS.production;
};

// API endpoints (same for all environments)
const API_ENDPOINTS = {
  health: '/health',
  status: '/api/status',
  analyzeSingleImage: '/api/analyze-multi-stage-base64', // Updated to use multi-stage analysis
  analyzeMultipleImages: '/api/analyze-multi-stage-base64', // Updated to use multi-stage analysis
  analysisStatus: '/api/analysis-status',
  // Legacy endpoints (for backward compatibility)
  legacyAnalyzeSingleImage: '/api/analyze-image-base64',
  legacyAnalyzeMultipleImages: '/api/analyze-images-base64'
};

// Export configuration
export const SERVER_CONFIG = {
  ...getCurrentConfig(),
  endpoints: API_ENDPOINTS,
  environment: isDevelopment ? 'development' : 'production'
};

// Helper functions
export const getServerUrl = () => SERVER_CONFIG.baseUrl;
export const getEndpoint = (name) => SERVER_CONFIG.endpoints[name];
export const getFullUrl = (endpointName) => `${SERVER_CONFIG.baseUrl}${SERVER_CONFIG.endpoints[endpointName]}`;

// Configuration update function (for runtime changes)
export const updateServerConfig = (newConfig) => {
  Object.assign(SERVER_CONFIG, newConfig);
  console.log('Server configuration updated:', SERVER_CONFIG);
};

// Debug information
export const getConfigInfo = () => {
  return {
    environment: SERVER_CONFIG.environment,
    baseUrl: SERVER_CONFIG.baseUrl,
    timeout: SERVER_CONFIG.timeout,
    retries: SERVER_CONFIG.retries,
    endpoints: Object.keys(SERVER_CONFIG.endpoints)
  };
};

console.log('Server configuration loaded:', getConfigInfo());
