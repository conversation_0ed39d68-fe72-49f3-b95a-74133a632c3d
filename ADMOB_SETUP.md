# AdMob Setup Guide for JunkChk

## ✅ What's Already Done

I've successfully set up Google AdMob in your Expo-managed React Native app with the following:

### Technical Implementation Completed:
1. **Installed Required Packages:**
   - `react-native-google-mobile-ads` (Official Google Mobile Ads SDK)
   - `expo-dev-client` (For development builds)
   - `expo-build-properties` (For native configuration)

2. **Updated app.json Configuration:**
   - Added iOS AdMob App ID: `ca-app-pub-****************~**********` ✅
   - Added Android AdMob App ID placeholder (test ID)
   - Added iOS bundle identifier: `com.akhilkirank.JunkChk`
   - Configured plugins for development build
   - Added iOS Info.plist settings for AdMob
   - Added SKAdNetwork identifiers for iOS 14+ compliance

3. **Created AdMob Components:**
   - `Components/AdMobBanner.tsx` - Reusable banner ad component
   - `services/AdMobService.ts` - AdMob initialization service

4. **Integrated into App:**
   - Added AdMob initialization in App.tsx
   - Added banner ads to HomeScreen.tsx
   - Updated with your real iOS ad unit ID: `ca-app-pub-****************/**********` ✅

## 🚀 How to Run with AdMob (Development Build Required)

Since `react-native-google-mobile-ads` requires native modules, you need to use a **development build** instead of Expo Go:

### Step 1: Create Development Build for iOS
```bash
# Option 1: Build for iOS Simulator (Recommended for testing)
npx expo run:ios --simulator

# Option 2: Build for physical iOS device (requires Apple Developer account)
npx expo run:ios --device

# If you get errors, try clearing cache first:
npx expo run:ios --clear
```

### Step 2: Run the Development Build
```bash
# Start the development server
npx expo start --dev-client

# Then use the development build app (not Expo Go) to scan the QR code
# The app will be installed as "JunkChk (development)" on your device/simulator
```

### Step 3: Verify AdMob Integration
1. Open the development build app
2. Navigate to the Home screen
3. You should see a banner ad below the stats section
4. Check the console logs for "AdMob initialized successfully"

## 📋 Manual Steps You Still Need to Complete

### ✅ COMPLETED: iOS AdMob Setup
- ✅ Created Google AdMob Account
- ✅ Registered iOS app "JunkChk" in AdMob
- ✅ Got iOS App ID: `ca-app-pub-****************~**********`
- ✅ Created iOS Banner Ad Unit: `ca-app-pub-****************/**********`
- ✅ Updated app.json and AdMobBanner.tsx with real IDs

### 🔄 REMAINING: Android AdMob Setup (Optional)
If you want to support Android:

1. **Register Android App in AdMob:**
   - In AdMob console: Apps → Add App → Android
   - App name: "JunkChk"
   - Package name: `com.akhilkirank.JunkChk`
   - Get your Android App ID

2. **Create Android Ad Units:**
   - Create a Banner Ad Unit for Android
   - Note the Android Ad Unit ID

3. **Update Configuration:**
   - Update `androidAppId` in app.json
   - Update Android ad unit ID in `Components/AdMobBanner.tsx`

## 🔧 Troubleshooting

### If you get "TurboModuleRegistry" error:
- Make sure you're using the development build, NOT Expo Go
- Run `npx expo run:ios` to create the development build
- Use the custom development build app to test

### If you get camera permission crash:
- ✅ FIXED: Added NSCameraUsageDescription to Info.plist
- The app now includes proper camera permissions

### If build fails:
- Clear cache: `npx expo start --clear`
- Clean iOS build: `npx expo run:ios --clear`
- Make sure Xcode is updated to latest version

## 📱 Testing

1. ✅ The app now shows real ads (production mode enabled)
2. Real AdMob IDs are configured and active
3. Banner ads appear on the HomeScreen below the stats section
4. Native ads are enabled and will show real advertisements

## 🎯 Next Steps

1. Create AdMob account and get real IDs
2. Replace test IDs with real ones
3. Build and test with development build
4. Submit to App Store (AdMob works in production builds)
